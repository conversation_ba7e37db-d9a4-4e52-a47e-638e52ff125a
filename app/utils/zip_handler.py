"""ZIP file handling utilities"""

import zipfile
import tempfile
import shutil
from pathlib import Path
from typing import List, Optional, Tuple


class ZipHandler:
    """Handles ZIP file creation and extraction"""

    @staticmethod
    def create_zip(
        files: List[Path], zip_path: Path, base_name: str = "output"
    ) -> Path:
        """
        Create ZIP file from list of files

        Args:
            files: List of file paths to include
            zip_path: Path where ZIP file should be created
            base_name: Base name for files in ZIP

        Returns:
            Path to created ZIP file
        """
        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for i, file_path in enumerate(files):
                if file_path.exists():
                    # Generate archive name
                    if len(files) == 1:
                        archive_name = f"{base_name}{file_path.suffix}"
                    else:
                        archive_name = f"{base_name}_{i + 1}{file_path.suffix}"

                    zipf.write(file_path, archive_name)

        return zip_path

    @staticmethod
    def should_zip_files(files: List[Path]) -> bool:
        """
        Determine if files should be zipped

        Returns True if there are multiple files or if single file is large
        """
        if len(files) > 1:
            return True

        if len(files) == 1:
            # ZIP if file is larger than 10MB
            file_size = files[0].stat().st_size if files[0].exists() else 0
            return file_size > 10 * 1024 * 1024

        return False

    @staticmethod
    def extract_zip(
        zip_path: Path, extract_dir: Optional[Path] = None
    ) -> Tuple[Path, Path]:
        """
        Extract ZIP file and identify the main document file

        Args:
            zip_path: Path to ZIP file
            extract_dir: Directory to extract to (creates temp dir if None)

        Returns:
            Tuple of (extraction_directory, main_file_path)

        Raises:
            ValueError: If no suitable main file is found
            zipfile.BadZipFile: If ZIP file is corrupted
        """
        if extract_dir is None:
            extract_dir = Path(tempfile.mkdtemp())
        else:
            extract_dir.mkdir(parents=True, exist_ok=True)

        # Extract ZIP file
        with zipfile.ZipFile(zip_path, "r") as zipf:
            zipf.extractall(extract_dir)

        # Find main document file
        main_file = ZipHandler._find_main_document(extract_dir)
        if not main_file:
            # Cleanup on failure
            if extract_dir.exists():
                shutil.rmtree(extract_dir)
            raise ValueError("No suitable main document file found in ZIP archive")

        return extract_dir, main_file

    @staticmethod
    def _find_main_document(directory: Path) -> Optional[Path]:
        """
        Find the main document file in extracted directory

        Looks for markdown files with priority:
        1. README.md, index.md, main.md (common main file names)
        2. Files in root directory over subdirectories
        3. Largest markdown file
        4. Any text-based document file

        Args:
            directory: Directory to search in

        Returns:
            Path to main document file or None if not found
        """
        # Define supported document extensions in order of preference
        markdown_extensions = {".md", ".markdown", ".mdown", ".mkd"}
        text_extensions = {".txt", ".rst", ".org", ".tex"}
        document_extensions = {".docx", ".odt", ".html", ".htm"}

        # Priority file names (case-insensitive)
        priority_names = {"readme", "index", "main", "document", "doc"}

        # Collect all potential files
        markdown_files = []
        text_files = []
        document_files = []

        # Search recursively but prefer root level files
        for file_path in directory.rglob("*"):
            if file_path.is_file():
                ext = file_path.suffix.lower()

                if ext in markdown_extensions:
                    markdown_files.append(file_path)
                elif ext in text_extensions:
                    text_files.append(file_path)
                elif ext in document_extensions:
                    document_files.append(file_path)

        # Helper function to check if file has priority name
        def has_priority_name(file_path: Path) -> bool:
            name = file_path.stem.lower()
            return name in priority_names

        # Helper function to get file depth (prefer root level)
        def get_file_depth(file_path: Path) -> int:
            return len(file_path.relative_to(directory).parts) - 1

        # Helper function to sort files by preference
        def sort_key(file_path: Path) -> tuple:
            return (
                not has_priority_name(file_path),  # Priority names first
                get_file_depth(file_path),  # Root level first
                -file_path.stat().st_size,  # Larger files first
            )

        # Try markdown files first (most likely to be main document)
        if markdown_files:
            markdown_files.sort(key=sort_key)
            return markdown_files[0]

        # Then try other text files
        if text_files:
            text_files.sort(key=sort_key)
            return text_files[0]

        # Finally try document files
        if document_files:
            document_files.sort(key=sort_key)
            return document_files[0]

        return None

    @staticmethod
    def is_zip_file(file_path: Path) -> bool:
        """
        Check if file is a valid ZIP file

        Args:
            file_path: Path to file to check

        Returns:
            True if file is a valid ZIP file
        """
        try:
            with zipfile.ZipFile(file_path, "r") as zipf:
                # Try to read the file list to verify it's a valid ZIP
                zipf.namelist()
                return True
        except (zipfile.BadZipFile, FileNotFoundError, PermissionError):
            return False

    @staticmethod
    def cleanup_extraction(extract_dir: Path) -> None:
        """
        Clean up extracted files

        Args:
            extract_dir: Directory to clean up

        Raises:
            FileProcessingError: If cleanup fails
        """
        try:
            if extract_dir.exists():
                shutil.rmtree(extract_dir)
        except Exception as e:
            from app.core.exception_handlers import FileProcessingError

            raise FileProcessingError(
                f"Failed to cleanup extraction directory: {extract_dir}",
                details={"directory": str(extract_dir), "error": str(e)},
            ) from e
