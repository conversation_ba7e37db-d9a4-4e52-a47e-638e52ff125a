"""Template management system for loading and managing templates"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

from pydantic import BaseModel, Field, ValidationError, field_validator
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.template import TemplateResource, ResourceType
from app.utils.file_handler import FileHandler

logger = logging.getLogger(__name__)


class TemplateMetadata(BaseModel):
    """Template metadata structure with Pydantic validation"""

    name: str = Field(
        default="", min_length=1, max_length=255, description="Template name"
    )
    display_name: str = Field(
        default="", min_length=1, max_length=255, description="Display name"
    )
    description: str = Field(
        default="", max_length=1000, description="Template description"
    )
    version: str = Field(
        default="1.0.0",
        pattern=r"^\d+\.\d+\.\d+$",
        description="Version number in semver format",
    )
    author: str = Field(default="", max_length=255, description="Author name")
    tags: List[str] = Field(default_factory=list, description="List of tags")
    supported_formats: List[str] = Field(
        default_factory=list, description="List of supported formats"
    )
    custom_fields: Dict[str, Any] = Field(
        default_factory=dict, description="Custom metadata fields"
    )

    @field_validator("tags")
    @classmethod
    def validate_tags(cls, v: List[str]) -> List[str]:
        """Validate tags list"""
        if not isinstance(v, list):
            raise ValueError("Tags must be a list")

        # Remove duplicates and empty strings
        cleaned_tags = list(set(tag.strip() for tag in v if tag and tag.strip()))

        # Validate each tag
        for tag in cleaned_tags:
            if len(tag) > 50:
                raise ValueError(f"Tag '{tag}' is too long (max 50 characters)")
            if not tag.replace("-", "").replace("_", "").isalnum():
                raise ValueError(f"Tag '{tag}' contains invalid characters")

        return cleaned_tags

    @field_validator("supported_formats")
    @classmethod
    def validate_supported_formats(cls, v: List[str]) -> List[str]:
        """Validate supported formats list"""
        if not isinstance(v, list):
            raise ValueError("Supported formats must be a list")

        # Remove duplicates and empty strings
        cleaned_formats = list(
            set(fmt.strip().lower() for fmt in v if fmt and fmt.strip())
        )

        # Validate each format
        valid_formats = {
            "docx",
            "pdf",
            "html",
            "markdown",
            "md",
            "txt",
            "rtf",
            "odt",
            "epub",
            "latex",
            "tex",
            "json",
            "xml",
        }

        for fmt in cleaned_formats:
            if fmt not in valid_formats:
                logger.warning(f"Unknown format '{fmt}' in supported_formats")

        return cleaned_formats

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TemplateMetadata":
        """Create from dictionary with validation"""
        try:
            return cls(**data)
        except ValidationError as e:
            logger.warning(f"Validation error in template metadata: {e}")
            # Return a default instance with safe values
            safe_name = data.get("name", "unknown")
            if not safe_name or len(safe_name) < 1:
                safe_name = "unknown"

            safe_display_name = data.get("display_name", "Unknown Template")
            if not safe_display_name or len(safe_display_name) < 1:
                safe_display_name = "Unknown Template"

            return cls(
                name=safe_name,
                display_name=safe_display_name,
                description=data.get("description", ""),
                version="1.0.0",
                author=data.get("author", ""),
                tags=[],
                supported_formats=[],
                custom_fields={},
            )


class TemplateManager:
    """Manages template loading, scanning, and metadata"""

    def __init__(self, db: Session):
        self.db = db
        self.template_dir = settings.template_dir
        self.builtin_templates_dir = self.template_dir / "builtin"
        self.user_templates_dir = self.template_dir / "user"
        self.additional_template_dirs = settings.get_additional_template_dirs()

        # Ensure directories exist
        self.builtin_templates_dir.mkdir(parents=True, exist_ok=True)
        self.user_templates_dir.mkdir(parents=True, exist_ok=True)

    async def scan_and_load_templates(self) -> Dict[str, int]:
        """Scan for templates and load them into database"""
        results = {
            "builtin_loaded": 0,
            "user_loaded": 0,
            "additional_loaded": 0,
            "errors": 0,
        }

        # Load built-in templates
        builtin_count, builtin_errors = await self._scan_directory(
            self.builtin_templates_dir, is_builtin=True, source="builtin"
        )
        results["builtin_loaded"] = builtin_count
        results["errors"] += builtin_errors

        # Load user templates
        user_count, user_errors = await self._scan_directory(
            self.user_templates_dir, is_builtin=False, source="user"
        )
        results["user_loaded"] = user_count
        results["errors"] += user_errors

        # Load additional template directories
        additional_count = 0
        for additional_dir in self.additional_template_dirs:
            if additional_dir.exists():
                count, errors = await self._scan_directory(
                    additional_dir,
                    is_builtin=False,
                    source=f"additional:{additional_dir.name}",
                )
                additional_count += count
                results["errors"] += errors
            else:
                logger.warning(
                    f"Additional template directory does not exist: {additional_dir}"
                )

        results["additional_loaded"] = additional_count

        return results

    async def _scan_directory(
        self, directory: Path, is_builtin: bool, source: str
    ) -> tuple[int, int]:
        """Scan a directory for templates"""
        loaded_count = 0
        error_count = 0

        if not directory.exists():
            return loaded_count, error_count

        # Scan for different resource types
        type_mappings = {
            ResourceType.DOCX_TEMPLATE: ["*.docx"],
            ResourceType.CSL_STYLE: ["*.csl"],
            ResourceType.LUA_FILTER: ["*.lua"],
            ResourceType.OTHER: ["*"],
        }

        for resource_type, patterns in type_mappings.items():
            for pattern in patterns:
                for file_path in directory.glob(f"**/{pattern}"):
                    if file_path.is_file() and not file_path.name.endswith(".metadata"):
                        try:
                            await self._load_template_file(
                                file_path, resource_type, is_builtin, source
                            )
                            loaded_count += 1
                        except Exception as e:
                            logger.error(f"Failed to load template {file_path}: {e}")
                            error_count += 1

        return loaded_count, error_count

    async def _load_template_file(
        self,
        file_path: Path,
        resource_type: ResourceType,
        is_builtin: bool,
        source: str,
    ) -> Optional[TemplateResource]:
        """Load a single template file with validation"""

        try:
            # Validate file exists and is readable
            if not file_path.exists():
                raise FileNotFoundError(f"Template file not found: {file_path}")

            if not file_path.is_file():
                raise ValueError(f"Path is not a file: {file_path}")

            # Generate template name from file path and source
            template_name = self._generate_template_name(file_path, source, is_builtin)

            # Validate template name
            if not template_name or len(template_name) > 255:
                raise ValueError(f"Invalid template name: {template_name}")

            # Check if template already exists
            existing = (
                self.db.query(TemplateResource)
                .filter(TemplateResource.name == template_name)
                .first()
            )

            if existing:
                # Update existing template if file is newer
                file_mtime = file_path.stat().st_mtime
                existing_timestamp = (
                    existing.updated_at.timestamp()
                    if existing.updated_at is not None
                    else existing.created_at.timestamp()
                )

                if file_mtime > existing_timestamp:
                    await self._update_existing_template(existing, file_path)
                return existing

            # Load metadata if available
            metadata = await self._load_template_metadata(file_path)

            # Validate file size
            file_size = file_path.stat().st_size
            if file_size > settings.max_file_size:
                raise ValueError(
                    f"File too large: {file_size} bytes (max: {settings.max_file_size})"
                )

            # Create new template resource
            template = TemplateResource(
                name=template_name,
                display_name=metadata.display_name
                or file_path.stem.replace("_", " ").title(),
                description=metadata.description,
                resource_type=resource_type.value,
                file_path=str(file_path.absolute()),
                file_size=file_size,
                mime_type=FileHandler.get_mime_type(file_path),
                is_builtin=is_builtin,
                resource_metadata=metadata.model_dump(),
            )

            self.db.add(template)
            self.db.commit()
            self.db.refresh(template)

            logger.info(f"Loaded template: {template_name} ({resource_type.value})")
            return template

        except Exception as e:
            logger.error(f"Failed to load template {file_path}: {e}")
            self.db.rollback()
            raise

    def _generate_template_name(
        self, file_path: Path, source: str, is_builtin: bool
    ) -> str:
        """Generate a unique template name from file path and source"""
        try:
            if source.startswith("additional:"):
                # For additional directories, use source prefix + relative path
                source_name = source.split(":", 1)[1]
                template_name = f"{source_name}_{file_path.stem}"
            elif is_builtin:
                relative_path = file_path.relative_to(self.builtin_templates_dir)
                template_name = (
                    str(relative_path.with_suffix(""))
                    .replace("/", "_")
                    .replace("\\", "_")
                )
            else:
                relative_path = file_path.relative_to(self.user_templates_dir)
                template_name = (
                    str(relative_path.with_suffix(""))
                    .replace("/", "_")
                    .replace("\\", "_")
                )

            # Clean up template name
            template_name = template_name.strip().replace(" ", "_")

            # Ensure name is not empty
            if not template_name:
                template_name = file_path.stem

            return template_name

        except Exception as e:
            logger.warning(f"Error generating template name for {file_path}: {e}")
            return file_path.stem

    async def _load_template_metadata(self, file_path: Path) -> TemplateMetadata:
        """Load metadata for a template file with validation"""

        # Look for metadata file (same name with .metadata extension)
        metadata_file = file_path.with_suffix(file_path.suffix + ".metadata")

        if metadata_file.exists():
            try:
                with open(metadata_file, "r", encoding="utf-8") as f:
                    metadata_dict = json.load(f)

                # Validate the loaded metadata
                if not isinstance(metadata_dict, dict):
                    raise ValueError("Metadata file must contain a JSON object")

                return TemplateMetadata.from_dict(metadata_dict)

            except json.JSONDecodeError as e:
                logger.warning(f"Invalid JSON in metadata file {metadata_file}: {e}")
            except ValidationError as e:
                logger.warning(f"Invalid metadata format in {metadata_file}: {e}")
            except Exception as e:
                logger.warning(f"Failed to load metadata for {file_path}: {e}")

        # Return default metadata with proper validation
        return TemplateMetadata(
            name=file_path.stem,
            display_name=file_path.stem.replace("_", " ").title(),
            description=f"Template file: {file_path.name}",
        )

    async def _update_existing_template(
        self, template: TemplateResource, file_path: Path
    ) -> None:
        """Update an existing template with new file data and validation"""

        try:
            # Validate file size
            file_size = file_path.stat().st_size
            if file_size > settings.max_file_size:
                raise ValueError(
                    f"File too large: {file_size} bytes (max: {settings.max_file_size})"
                )

            template.file_size = file_size
            template.mime_type = FileHandler.get_mime_type(file_path)

            # Update metadata if available
            metadata = await self._load_template_metadata(file_path)
            template.set_metadata(metadata.model_dump())

            self.db.commit()
            logger.info(f"Updated template: {template.name}")

        except Exception as e:
            logger.error(f"Failed to update template {template.name}: {e}")
            self.db.rollback()
            raise

    async def reload_templates(self) -> Dict[str, int]:
        """Reload all templates from filesystem"""
        logger.info("Reloading templates...")
        return await self.scan_and_load_templates()

    def get_template_by_name(self, name: str) -> Optional[TemplateResource]:
        """Get template by name"""
        return (
            self.db.query(TemplateResource)
            .filter(TemplateResource.name == name, TemplateResource.is_active)
            .first()
        )

    def get_templates_by_type(
        self, resource_type: ResourceType
    ) -> List[TemplateResource]:
        """Get all templates of a specific type"""
        return (
            self.db.query(TemplateResource)
            .filter(
                TemplateResource.resource_type == resource_type.value,
                TemplateResource.is_active,
            )
            .all()
        )

    def search_templates(
        self,
        query: str = "",
        resource_type: Optional[ResourceType] = None,
        tags: List[str] = None,
    ) -> List[TemplateResource]:
        """Search templates by query, type, and tags"""

        db_query = self.db.query(TemplateResource).filter(TemplateResource.is_active)

        if resource_type:
            db_query = db_query.filter(
                TemplateResource.resource_type == resource_type.value
            )

        if query:
            db_query = db_query.filter(
                TemplateResource.display_name.contains(query)
                | TemplateResource.description.contains(query)
            )

        templates = db_query.all()

        # Filter by tags if specified
        if tags:
            filtered_templates = []
            for template in templates:
                template_tags = template.get_metadata().get("tags", [])
                if any(tag in template_tags for tag in tags):
                    filtered_templates.append(template)
            return filtered_templates

        return templates
