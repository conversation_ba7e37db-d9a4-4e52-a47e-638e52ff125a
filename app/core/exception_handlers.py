"""Custom exception handlers with <PERSON> formatting"""

import traceback
from typing import Any, Dict

from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from rich.console import Console
from rich.traceback import Traceback

from app.core.logging_config import get_logger, log_exception

logger = get_logger(__name__)
console = Console()


class PandocAPIException(Exception):
    """Base exception for Pandoc API"""

    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Dict[str, Any] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class PandocConversionError(PandocAPIException):
    """Exception raised when Pandoc conversion fails"""

    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
        )


class TemplateNotFoundError(PandocAPIException):
    """Exception raised when a template is not found"""

    def __init__(self, template_name: str, template_type: str = "template"):
        message = f"{template_type.title()} '{template_name}' not found"
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details={"template_name": template_name, "template_type": template_type},
        )


class FileProcessingError(PandocAPIException):
    """Exception raised when file processing fails"""

    def __init__(
        self, message: str, filename: str = None, details: Dict[str, Any] = None
    ):
        details = details or {}
        if filename:
            details["filename"] = filename
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details,
        )


class ValidationError(PandocAPIException):
    """Exception raised when validation fails"""

    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        details = details or {}
        if field:
            details["field"] = field
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
        )


async def pandoc_api_exception_handler(
    request: Request, exc: PandocAPIException
) -> JSONResponse:
    """Handle custom Pandoc API exceptions"""

    log_exception(
        logger, exc, f"PandocAPIException in {request.method} {request.url.path}"
    )

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "type": exc.__class__.__name__,
                "message": exc.message,
                "details": exc.details,
                "path": str(request.url.path),
                "method": request.method,
            }
        },
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""

    logger.warning(
        f"HTTP {exc.status_code} in {request.method} {request.url.path}: {exc.detail}"
    )

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "type": "HTTPException",
                "message": exc.detail,
                "status_code": exc.status_code,
                "path": str(request.url.path),
                "method": request.method,
            }
        },
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions"""

    # Log the full traceback with Rich formatting
    log_exception(
        logger, exc, f"Unhandled exception in {request.method} {request.url.path}"
    )

    # In debug mode, include traceback in response
    error_content = {
        "error": {
            "type": exc.__class__.__name__,
            "message": str(exc),
            "path": str(request.url.path),
            "method": request.method,
        }
    }

    # Add traceback in debug mode
    from app.core.config import settings

    if settings.debug:
        error_content["error"]["traceback"] = traceback.format_exc()

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_content,
    )


async def validation_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """Handle Pydantic validation exceptions"""

    logger.warning(f"Validation error in {request.method} {request.url.path}: {exc}")

    # Extract validation errors if it's a Pydantic ValidationError
    details = {}
    if hasattr(exc, "errors"):
        details["validation_errors"] = exc.errors()

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": {
                "type": "ValidationError",
                "message": "Request validation failed",
                "details": details,
                "path": str(request.url.path),
                "method": request.method,
            }
        },
    )


def format_exception_for_console(exc: Exception, request: Request = None) -> None:
    """Format and print exception to console using Rich"""

    if request:
        console.print(
            f"\n[bold red]Exception in {request.method} {request.url.path}[/bold red]"
        )
    else:
        console.print(f"\n[bold red]Exception occurred[/bold red]")

    # Create Rich traceback
    tb = Traceback.from_exception(
        type(exc),
        exc,
        exc.__traceback__,
        show_locals=True,
        suppress=["fastapi", "uvicorn", "starlette"],
    )

    console.print(tb)


def setup_exception_handlers(app):
    """Setup all exception handlers for the FastAPI app"""

    # Custom exception handlers
    app.add_exception_handler(PandocAPIException, pandoc_api_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)

    # Pydantic validation errors
    try:
        from pydantic import ValidationError as PydanticValidationError

        app.add_exception_handler(PydanticValidationError, validation_exception_handler)
    except ImportError:
        pass

    # General exception handler (catch-all)
    app.add_exception_handler(Exception, general_exception_handler)

    logger.info("Exception handlers configured with Rich formatting")
