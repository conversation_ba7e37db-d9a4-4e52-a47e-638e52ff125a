"""Rich logging configuration for the application"""

import logging
import sys
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.traceback import install as install_rich_traceback

from app.core.config import settings


def setup_logging(
    level: Optional[str] = None,
    format_type: Optional[str] = None,
    log_file: Optional[str] = None,
) -> None:
    """
    Setup logging with Rich formatting

    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_type: Format type ("rich", "json", or "standard")
        log_file: Optional log file path
    """
    # Use settings defaults if not provided
    level = level or settings.log_level
    format_type = format_type or settings.log_format
    log_file = log_file or settings.log_file

    # Install rich traceback handler for better exception formatting
    install_rich_traceback(
        console=Console(stderr=True),
        show_locals=settings.debug,
        suppress=[
            "fastapi",
            "uvicorn",
            "starlette",
        ],
    )

    # Clear existing handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    # Set log level
    log_level = getattr(logging, level.upper(), logging.INFO)
    root_logger.setLevel(log_level)

    handlers = []

    if format_type == "rich":
        # Rich console handler
        console = Console(stderr=True)
        rich_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=True,
            show_level=True,
            markup=True,
            rich_tracebacks=True,
            tracebacks_show_locals=settings.debug,
            tracebacks_suppress=[
                "fastapi",
                "uvicorn",
                "starlette",
            ],
        )
        rich_handler.setLevel(log_level)
        handlers.append(rich_handler)

    elif format_type == "json":
        # JSON formatter for structured logging
        import json
        import datetime

        class JSONFormatter(logging.Formatter):
            def format(self, record):
                log_entry = {
                    "timestamp": datetime.datetime.fromtimestamp(
                        record.created
                    ).isoformat(),
                    "level": record.levelname,
                    "logger": record.name,
                    "message": record.getMessage(),
                    "module": record.module,
                    "function": record.funcName,
                    "line": record.lineno,
                }

                if record.exc_info:
                    log_entry["exception"] = self.formatException(record.exc_info)

                return json.dumps(log_entry, ensure_ascii=False)

        json_handler = logging.StreamHandler(sys.stderr)
        json_handler.setFormatter(JSONFormatter())
        json_handler.setLevel(log_level)
        handlers.append(json_handler)

    else:
        # Standard formatter
        formatter = logging.Formatter(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        standard_handler = logging.StreamHandler(sys.stderr)
        standard_handler.setFormatter(formatter)
        standard_handler.setLevel(log_level)
        handlers.append(standard_handler)

    # Add file handler if specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.FileHandler(log_path, encoding="utf-8")

        if format_type == "rich":
            # Use standard format for file logging even with rich console
            file_formatter = logging.Formatter(
                fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
            )
            file_handler.setFormatter(file_formatter)
        elif format_type == "json":
            file_handler.setFormatter(JSONFormatter())
        else:
            file_handler.setFormatter(formatter)

        file_handler.setLevel(log_level)
        handlers.append(file_handler)

    # Add all handlers to root logger
    for handler in handlers:
        root_logger.addHandler(handler)

    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(
        logging.WARNING
    )  # We handle access logs in middleware
    logging.getLogger("uvicorn.error").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)

    # HTTP access logger (our custom middleware)
    logging.getLogger("http.access").setLevel(log_level)

    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        f"[bold green]Logging configured[/bold green] - "
        f"Level: {level}, Format: {format_type}"
        + (f", File: {log_file}" if log_file else ""),
        extra={"markup": True} if format_type == "rich" else {},
    )


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name"""
    return logging.getLogger(name)


def log_exception(
    logger: logging.Logger, exc: Exception, message: str = "An error occurred"
) -> None:
    """
    Log an exception with rich formatting

    Args:
        logger: Logger instance
        exc: Exception to log
        message: Additional message
    """
    if settings.log_format == "rich":
        logger.exception(
            f"[bold red]{message}[/bold red]: {exc}", extra={"markup": True}
        )
    else:
        logger.exception(f"{message}: {exc}")


def log_success(logger: logging.Logger, message: str) -> None:
    """
    Log a success message with rich formatting

    Args:
        logger: Logger instance
        message: Success message
    """
    if settings.log_format == "rich":
        logger.info(f"[bold green]✓[/bold green] {message}", extra={"markup": True})
    else:
        logger.info(f"✓ {message}")


def log_warning(logger: logging.Logger, message: str) -> None:
    """
    Log a warning message with rich formatting

    Args:
        logger: Logger instance
        message: Warning message
    """
    if settings.log_format == "rich":
        logger.warning(
            f"[bold yellow]⚠[/bold yellow] {message}", extra={"markup": True}
        )
    else:
        logger.warning(f"⚠ {message}")


def log_error(logger: logging.Logger, message: str) -> None:
    """
    Log an error message with rich formatting

    Args:
        logger: Logger instance
        message: Error message
    """
    if settings.log_format == "rich":
        logger.error(f"[bold red]✗[/bold red] {message}", extra={"markup": True})
    else:
        logger.error(f"✗ {message}")
