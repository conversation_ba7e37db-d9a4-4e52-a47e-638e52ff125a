"""File cleanup service for managing expired files"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.models.file_record import FileRecord
from app.utils.file_handler import FileHandler


logger = logging.getLogger(__name__)


class FileCleanupService:
    """Service for cleaning up expired files"""

    def __init__(self):
        self.is_running = False
        self._cleanup_task: Optional[asyncio.Task] = None

    async def start(self):
        """Start the file cleanup service"""
        if self.is_running:
            logger.warning("File cleanup service is already running")
            return

        if not settings.enable_auto_cleanup:
            logger.info("Auto cleanup is disabled in settings")
            return

        self.is_running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info(
            f"File cleanup service started with {settings.cleanup_interval_hours}h interval"
        )

    async def stop(self):
        """Stop the file cleanup service"""
        if not self.is_running:
            return

        self.is_running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        logger.info("File cleanup service stopped")

    async def _cleanup_loop(self):
        """Main cleanup loop"""
        while self.is_running:
            try:
                await self.cleanup_expired_files()
                # Wait for the next cleanup interval
                await asyncio.sleep(settings.cleanup_interval_hours * 3600)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(300)  # 5 minutes

    async def cleanup_expired_files(self) -> dict:
        """
        Clean up expired files

        Returns:
            Dictionary with cleanup statistics
        """
        logger.info("Starting file cleanup process")

        stats = {
            "files_checked": 0,
            "files_deleted": 0,
            "files_failed": 0,
            "disk_space_freed": 0,
        }

        try:
            db = next(get_db())

            # Get all expired files
            expired_files = self._get_expired_files(db)
            stats["files_checked"] = len(expired_files)

            for file_record in expired_files:
                try:
                    if await self._cleanup_file(file_record, db):
                        stats["files_deleted"] += 1
                        stats["disk_space_freed"] += file_record.file_size
                    else:
                        stats["files_failed"] += 1
                except Exception as e:
                    logger.error(f"Failed to cleanup file {file_record.filename}: {e}")
                    stats["files_failed"] += 1

            # Also cleanup orphaned files (files on disk without database records)
            orphaned_stats = await self._cleanup_orphaned_files(db)
            stats["files_checked"] += orphaned_stats["files_checked"]
            stats["files_deleted"] += orphaned_stats["files_deleted"]
            stats["disk_space_freed"] += orphaned_stats["disk_space_freed"]

            logger.info(f"File cleanup completed: {stats}")

        except Exception as e:
            logger.error(f"Error during file cleanup: {e}")

        return stats

    def _get_expired_files(self, db: Session) -> List[FileRecord]:
        """Get all expired files from database"""
        now = datetime.utcnow()
        return (
            db.query(FileRecord)
            .filter(
                FileRecord.expires_at.isnot(None),
                FileRecord.expires_at < now,
                FileRecord.is_deleted == False,
                FileRecord.is_active == True,
            )
            .all()
        )

    async def _cleanup_file(self, file_record: FileRecord, db: Session) -> bool:
        """
        Clean up a single file

        Args:
            file_record: File record to cleanup
            db: Database session

        Returns:
            True if file was successfully cleaned up
        """
        try:
            file_path = Path(file_record.file_path)

            # Delete file from disk if it exists
            if file_path.exists():
                FileHandler.delete_file(file_path)
                logger.debug(f"Deleted file: {file_path}")

            # Mark as deleted in database
            file_record.mark_deleted()
            db.commit()

            logger.info(f"Cleaned up expired file: {file_record.filename}")
            return True

        except Exception as e:
            logger.error(f"Failed to cleanup file {file_record.filename}: {e}")
            db.rollback()
            return False

    async def _cleanup_orphaned_files(self, db: Session) -> dict:
        """
        Clean up orphaned files (files on disk without database records)

        Returns:
            Dictionary with cleanup statistics
        """
        stats = {
            "files_checked": 0,
            "files_deleted": 0,
            "disk_space_freed": 0,
        }

        try:
            # Check upload directory
            upload_stats = await self._cleanup_orphaned_in_directory(
                settings.upload_dir, db, "upload"
            )

            # Check output directory
            output_stats = await self._cleanup_orphaned_in_directory(
                settings.output_dir, db, "output"
            )

            # Combine stats
            for key in stats:
                stats[key] = upload_stats.get(key, 0) + output_stats.get(key, 0)

        except Exception as e:
            logger.error(f"Error cleaning up orphaned files: {e}")

        return stats

    async def _cleanup_orphaned_in_directory(
        self, directory: Path, db: Session, file_type: str
    ) -> dict:
        """Clean up orphaned files in a specific directory"""
        stats = {
            "files_checked": 0,
            "files_deleted": 0,
            "disk_space_freed": 0,
        }

        if not directory.exists():
            return stats

        try:
            # Get all files in directory (recursively)
            for file_path in directory.rglob("*"):
                if not file_path.is_file():
                    continue

                stats["files_checked"] += 1

                # Check if file exists in database
                file_record = (
                    db.query(FileRecord)
                    .filter(FileRecord.file_path == str(file_path))
                    .first()
                )

                if file_record is None:
                    # Orphaned file - check if it's old enough to delete
                    file_age = datetime.utcnow() - datetime.fromtimestamp(
                        file_path.stat().st_mtime
                    )

                    # Delete files older than the expiry time for this type
                    max_age_hours = (
                        settings.upload_file_expiry_hours
                        if file_type == "upload"
                        else settings.output_file_expiry_hours
                    )

                    if (
                        max_age_hours > 0
                        and file_age.total_seconds() > max_age_hours * 3600
                    ):
                        try:
                            file_size = file_path.stat().st_size
                            FileHandler.delete_file(file_path)
                            stats["files_deleted"] += 1
                            stats["disk_space_freed"] += file_size
                            logger.info(f"Deleted orphaned file: {file_path}")
                        except Exception as e:
                            logger.error(
                                f"Failed to delete orphaned file {file_path}: {e}"
                            )

        except Exception as e:
            logger.error(f"Error scanning directory {directory}: {e}")

        return stats


# Global cleanup service instance
cleanup_service = FileCleanupService()


async def start_cleanup_service():
    """Start the global cleanup service"""
    await cleanup_service.start()


async def stop_cleanup_service():
    """Stop the global cleanup service"""
    await cleanup_service.stop()


async def manual_cleanup() -> dict:
    """Manually trigger file cleanup"""
    return await cleanup_service.cleanup_expired_files()
