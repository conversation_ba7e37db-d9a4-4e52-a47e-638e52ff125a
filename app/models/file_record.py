"""File record models for tracking uploaded and generated files"""

from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, Text, Bo<PERSON>an, BigInteger
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.config import settings


class FileType(str, Enum):
    """
    文件类型枚举

    File type enumeration
    """

    UPLOAD = "upload"  # 上传的文件 / Uploaded file
    OUTPUT = "output"  # 转换输出的文件 / Conversion output file
    TEMPLATE = "template"  # 模板文件 / Template file


class FileRecord(Base):
    """File record model for tracking file lifecycle"""

    __tablename__ = "file_records"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False, index=True)
    original_filename = Column(String(255), nullable=True)  # 原始文件名
    file_path = Column(String(500), nullable=False)  # 文件存储路径
    file_type = Column(
        String(20), nullable=False, index=True
    )  # upload, output, template
    file_size = Column(BigInteger, nullable=False)  # 文件大小(字节)
    mime_type = Column(String(100), nullable=True)

    # 时间相关字段
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    expires_at = Column(DateTime(timezone=True), nullable=True, index=True)  # 过期时间
    accessed_at = Column(DateTime(timezone=True), nullable=True)  # 最后访问时间

    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # 关联信息
    conversion_id = Column(String(100), nullable=True, index=True)  # 关联的转换任务ID
    user_session = Column(String(100), nullable=True, index=True)  # 用户会话ID

    # 元数据
    file_metadata = Column(Text, nullable=True)  # JSON格式的额外元数据

    def __repr__(self):
        return f"<FileRecord(filename='{self.filename}', type='{self.file_type}', expires_at='{self.expires_at}')>"

    @property
    def file_exists(self) -> bool:
        """Check if the file exists on disk"""
        return Path(self.file_path).exists()

    @property
    def is_expired(self) -> bool:
        """Check if the file has expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    @property
    def time_until_expiry(self) -> Optional[timedelta]:
        """Get time until expiry, None if no expiry set"""
        if self.expires_at is None:
            return None
        return self.expires_at - datetime.utcnow()

    def get_absolute_path(self) -> Path:
        """Get absolute path to the file"""
        return Path(self.file_path).resolve()

    def mark_accessed(self) -> None:
        """Mark file as accessed (update accessed_at timestamp)"""
        self.accessed_at = datetime.utcnow()

    def mark_deleted(self) -> None:
        """Mark file as deleted (soft delete)"""
        self.is_deleted = True
        self.is_active = False

    @classmethod
    def calculate_expiry_time(cls, file_type: FileType) -> Optional[datetime]:
        """Calculate expiry time based on file type and settings"""
        now = datetime.utcnow()

        if file_type == FileType.UPLOAD:
            if settings.upload_file_expiry_hours > 0:
                return now + timedelta(hours=settings.upload_file_expiry_hours)
        elif file_type == FileType.OUTPUT:
            if settings.output_file_expiry_hours > 0:
                return now + timedelta(hours=settings.output_file_expiry_hours)
        elif file_type == FileType.TEMPLATE:
            if settings.template_file_expiry_hours > 0:
                return now + timedelta(hours=settings.template_file_expiry_hours)

        return None  # No expiry

    @classmethod
    def create_record(
        cls,
        filename: str,
        file_path: str,
        file_type: FileType,
        file_size: int,
        original_filename: Optional[str] = None,
        mime_type: Optional[str] = None,
        conversion_id: Optional[str] = None,
        user_session: Optional[str] = None,
        file_metadata: Optional[str] = None,
    ) -> "FileRecord":
        """Create a new file record with automatic expiry calculation"""
        expires_at = cls.calculate_expiry_time(file_type)

        return cls(
            filename=filename,
            original_filename=original_filename,
            file_path=file_path,
            file_type=file_type.value,
            file_size=file_size,
            mime_type=mime_type,
            expires_at=expires_at,
            conversion_id=conversion_id,
            user_session=user_session,
            file_metadata=file_metadata,
        )
